// test-error-handler.js
// 简单的测试脚本来验证错误处理是否正常工作

const express = require('express');
const rateLimit = require('express-rate-limit');

const app = express();

// 模拟 trust proxy 设置为 true 的情况
app.set('trust proxy', true);

// 创建一个简单的 rate limiter
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP限制100次请求
  message: { ok: false, message: "请求过于频繁，请稍后再试" }
});

// 模拟错误处理中间件
app.use((error, req, res, next) => {
  if (error.name === 'ValidationError' && error.code === 'ERR_ERL_PERMISSIVE_TRUST_PROXY') {
    console.log('✅ 成功捕获 Express Rate Limit Trust Proxy 错误:', error.message);
    next();
    return;
  }
  
  console.log('❌ 未处理的错误:', error);
  res.status(500).json({ error: 'Internal Server Error' });
});

app.use(limiter);

app.get('/test', (req, res) => {
  res.json({ message: 'Test successful' });
});

const port = 3001;
app.listen(port, () => {
  console.log(`测试服务器运行在端口 ${port}`);
  console.log('请访问 http://localhost:3001/test 来测试');
});
