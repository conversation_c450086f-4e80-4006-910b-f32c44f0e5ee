#!/usr/bin/env node

/**
 * 测试农场区块优化效果
 * 验证 getMaxActiveGrade 调用次数是否减少
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3456/api';
const TEST_WALLET_ID = 1; // 使用一个测试用户ID

// 创建认证头
function createAuthHeaders() {
  return {
    'wallet-id': TEST_WALLET_ID,
    'Content-Type': 'application/json'
  };
}

// 测试农场区块API
async function testFarmPlotsAPI() {
  console.log('🧪 测试农场区块API优化效果\n');

  try {
    console.log('📊 调用 /api/farm/farm-plots 接口...');
    const startTime = Date.now();
    
    const response = await axios.get(`${BASE_URL}/farm/farm-plots`, {
      headers: createAuthHeaders()
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`✅ 请求成功: ${response.status}`);
    console.log(`⏱️  响应时间: ${duration}ms`);
    console.log(`📦 返回数据: ${response.data.data.farmPlots.length} 个农场区块`);
    
    // 显示第一个区块的详细信息
    if (response.data.data.farmPlots.length > 0) {
      const firstPlot = response.data.data.farmPlots[0];
      console.log(`\n📋 第一个区块详情:`);
      console.log(`   - 等级: ${firstPlot.level}`);
      console.log(`   - 是否解锁: ${firstPlot.isUnlocked}`);
      console.log(`   - 生产速度: ${firstPlot.productionSpeed}秒`);
      console.log(`   - 牛奶产量: ${firstPlot.milk}`);
      console.log(`   - 有升级预览: ${!!firstPlot.nextUpgradeGrowth}`);
    }
    
    return {
      success: true,
      duration,
      plotCount: response.data.data.farmPlots.length
    };
    
  } catch (error) {
    console.error('❌ 请求失败:', error.response?.data || error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// 连续测试多次，观察性能
async function performanceTest() {
  console.log('🚀 开始性能测试...\n');
  
  const results = [];
  const testCount = 5;
  
  for (let i = 1; i <= testCount; i++) {
    console.log(`📊 第 ${i}/${testCount} 次测试:`);
    const result = await testFarmPlotsAPI();
    results.push(result);
    
    if (i < testCount) {
      console.log('⏳ 等待 2 秒...\n');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // 统计结果
  const successfulResults = results.filter(r => r.success);
  if (successfulResults.length > 0) {
    const avgDuration = successfulResults.reduce((sum, r) => sum + r.duration, 0) / successfulResults.length;
    const minDuration = Math.min(...successfulResults.map(r => r.duration));
    const maxDuration = Math.max(...successfulResults.map(r => r.duration));
    
    console.log('\n📈 性能统计:');
    console.log(`   - 成功请求: ${successfulResults.length}/${testCount}`);
    console.log(`   - 平均响应时间: ${avgDuration.toFixed(2)}ms`);
    console.log(`   - 最快响应时间: ${minDuration}ms`);
    console.log(`   - 最慢响应时间: ${maxDuration}ms`);
  }
}

// 检查日志中的数据库查询次数
function showOptimizationTips() {
  console.log('\n💡 优化效果验证:');
  console.log('1. 查看服务器日志，观察 "从数据库获取最高等级" 的打印次数');
  console.log('2. 优化前：每个已解锁的牧场区都会调用一次 getMaxActiveGrade()');
  console.log('3. 优化后：整个请求只调用一次 getMaxActiveGrade()');
  console.log('4. 如果用户有5个已解锁牧场区，优化后数据库查询次数从5次减少到1次');
  
  console.log('\n🔍 日志监控命令:');
  console.log('   tail -f logs/app.log | grep "从数据库获取最高等级"');
  
  console.log('\n📊 预期效果:');
  console.log('   - 数据库查询次数显著减少');
  console.log('   - API响应时间略有改善');
  console.log('   - 服务器负载降低');
}

// 主函数
async function main() {
  console.log('🎯 农场区块优化效果测试');
  console.log('='.repeat(50));
  
  // 单次测试
  console.log('\n1️⃣ 单次测试:');
  await testFarmPlotsAPI();
  
  console.log('\n' + '='.repeat(50));
  
  // 性能测试
  console.log('\n2️⃣ 性能测试:');
  await performanceTest();
  
  console.log('\n' + '='.repeat(50));
  
  // 显示优化提示
  showOptimizationTips();
  
  console.log('\n✅ 测试完成!');
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testFarmPlotsAPI, performanceTest };
