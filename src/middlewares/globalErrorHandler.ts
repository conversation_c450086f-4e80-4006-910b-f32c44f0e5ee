// src/middlewares/globalErrorHandler.ts
import { Request, Response, NextFunction } from 'express';
import { logger, formatError } from '../utils/logger';

/**
 * 全局错误处理中间件
 * 统一处理应用中的各种错误，包括 express-rate-limit 的 ValidationError
 */
export function globalErrorHandler(error: any, req: Request, res: Response, next: NextFunction): void {
  // 处理 express-rate-limit 的 Trust Proxy ValidationError
  if (error.name === 'ValidationError' && error.code === 'ERR_ERL_PERMISSIVE_TRUST_PROXY') {
    logger.warn('Express Rate Limit Trust Proxy Configuration Warning', {
      error: error.message,
      code: error.code,
      help: error.help || 'https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/',
      url: req.url,
      method: req.method,
      ip: req.ip
    });
    // 这个错误不需要响应客户端，继续处理请求
    next();
    return;
  }

  // 处理其他 express-rate-limit ValidationError
  if (error.name === 'ValidationError' && error.code?.startsWith('ERR_ERL_')) {
    logger.error('Express Rate Limit Validation Error', {
      error: error.message,
      code: error.code,
      help: error.help,
      url: req.url,
      method: req.method,
      ip: req.ip
    });

    if (!res.headersSent) {
      res.status(400).json({
        ok: false,
        message: 'Rate limit configuration error',
        error: error.message
      });
    }
    return;
  }

  // 处理一般的 ValidationError
  if (error.name === 'ValidationError') {
    logger.error('Validation Error', {
      ...formatError(error),
      url: req.url,
      method: req.method,
      ip: req.ip
    });

    if (!res.headersSent) {
      res.status(400).json({
        ok: false,
        message: 'Request validation failed',
        error: error.message
      });
    }
    return;
  }

  // 处理 Multer 文件上传错误
  if (error.code === 'LIMIT_FILE_SIZE') {
    logger.warn('File Upload Size Limit Exceeded', {
      error: error.message,
      url: req.url,
      method: req.method,
      ip: req.ip
    });

    if (!res.headersSent) {
      res.status(400).json({
        ok: false,
        message: 'File size exceeds limit'
      });
    }
    return;
  }

  // 处理 JWT 相关错误
  if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
    logger.warn('JWT Authentication Error', {
      ...formatError(error),
      url: req.url,
      method: req.method,
      ip: req.ip
    });

    if (!res.headersSent) {
      res.status(401).json({
        ok: false,
        message: 'Authentication failed'
      });
    }
    return;
  }

  // 处理数据库相关错误
  if (error.name === 'SequelizeError' || error.name?.startsWith('Sequelize')) {
    logger.error('Database Error', {
      ...formatError(error),
      url: req.url,
      method: req.method,
      ip: req.ip
    });

    if (!res.headersSent) {
      res.status(500).json({
        ok: false,
        message: 'Database operation failed'
      });
    }
    return;
  }

  // 处理其他未捕获的错误
  logger.error('Unhandled Application Error', {
    ...formatError(error),
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.method === 'POST' || req.method === 'PUT' ? req.body : undefined
  });

  if (!res.headersSent) {
    res.status(500).json({
      ok: false,
      message: 'Internal server error'
    });
  }
}

/**
 * 404 错误处理中间件
 * 处理未找到的路由
 */
export function notFoundHandler(req: Request, res: Response): void {
  logger.warn('404 - Route Not Found', {
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(404).json({
    ok: false,
    message: 'Requested resource not found'
  });
}
