// test-global-error-handler.ts
// 测试全局错误处理中间件

import { Request, Response, NextFunction } from 'express';
import { globalErrorHandler } from './src/middlewares/globalErrorHandler';

// 模拟 logger
const mockLogger = {
  warn: (message: string, data?: any) => {
    console.log(`✅ WARN: ${message}`, data);
  },
  error: (message: string, data?: any) => {
    console.log(`❌ ERROR: ${message}`, data);
  }
};

// 替换真实的 logger
jest.mock('./src/utils/logger', () => ({
  logger: mockLogger,
  formatError: (error: any) => ({
    error: error.message,
    stack: error.stack
  })
}));

// 模拟请求和响应对象
const mockRequest = {
  url: '/api/test',
  method: 'POST',
  ip: '*************',
  get: (header: string) => 'Mozilla/5.0',
  body: { test: 'data' }
} as unknown as Request;

const mockResponse = {
  headersSent: false,
  status: jest.fn().mockReturnThis(),
  json: jest.fn().mockReturnThis()
} as unknown as Response;

const mockNext = jest.fn() as NextFunction;

describe('Global Error Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (mockResponse as any).headersSent = false;
  });

  test('应该正确处理 ERR_ERL_PERMISSIVE_TRUST_PROXY 错误', () => {
    const error = {
      name: 'ValidationError',
      code: 'ERR_ERL_PERMISSIVE_TRUST_PROXY',
      message: 'Trust proxy warning',
      help: 'https://example.com/help'
    };

    globalErrorHandler(error, mockRequest, mockResponse, mockNext);

    // 应该调用 next() 继续处理
    expect(mockNext).toHaveBeenCalled();
    
    // 不应该发送响应
    expect(mockResponse.status).not.toHaveBeenCalled();
    expect(mockResponse.json).not.toHaveBeenCalled();
  });

  test('应该正确处理一般的 ValidationError', () => {
    const error = {
      name: 'ValidationError',
      message: 'Validation failed',
      stack: 'Error stack trace'
    };

    globalErrorHandler(error, mockRequest, mockResponse, mockNext);

    // 应该发送 400 响应
    expect(mockResponse.status).toHaveBeenCalledWith(400);
    expect(mockResponse.json).toHaveBeenCalledWith({
      ok: false,
      message: '请求验证失败',
      error: 'Validation failed'
    });
  });

  test('应该正确处理未知错误', () => {
    const error = {
      name: 'UnknownError',
      message: 'Something went wrong',
      stack: 'Error stack trace'
    };

    globalErrorHandler(error, mockRequest, mockResponse, mockNext);

    // 应该发送 500 响应
    expect(mockResponse.status).toHaveBeenCalledWith(500);
    expect(mockResponse.json).toHaveBeenCalledWith({
      ok: false,
      message: '服务器内部错误'
    });
  });

  test('当响应已发送时不应该再次发送响应', () => {
    (mockResponse as any).headersSent = true;
    
    const error = {
      name: 'ValidationError',
      message: 'Validation failed'
    };

    globalErrorHandler(error, mockRequest, mockResponse, mockNext);

    // 不应该发送响应
    expect(mockResponse.status).not.toHaveBeenCalled();
    expect(mockResponse.json).not.toHaveBeenCalled();
  });
});

console.log('✅ 全局错误处理中间件测试完成');
console.log('现在 express-rate-limit 的 ValidationError 将通过统一的日志系统输出');
console.log('错误类型：');
console.log('- ERR_ERL_PERMISSIVE_TRUST_PROXY: 使用 logger.warn 记录，不中断请求');
console.log('- 其他 ValidationError: 使用 logger.error 记录，返回 400 错误');
console.log('- 未知错误: 使用 logger.error 记录，返回 500 错误');
